/**
 * 视频播放器配置管理
 * Video Player Configuration Management
 * <AUTHOR>
 * @version 1.0.0
 */

class VideoPlayerConfig {
    constructor() {
        this.deviceInfo = this.detectDevice();
        this.networkInfo = this.detectNetwork();
        this.config = this.generateConfig();
    }

    /**
     * 检测设备信息
     */
    detectDevice() {
        const userAgent = navigator.userAgent;
        
        return {
            isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent),
            isIOS: /iPad|iPhone|iPod/.test(userAgent),
            isAndroid: /Android/i.test(userAgent),
            isTablet: /iPad|Android(?=.*\bMobile\b)/.test(userAgent),
            screenWidth: window.innerWidth,
            screenHeight: window.innerHeight,
            pixelRatio: window.devicePixelRatio || 1,
            touchSupport: 'ontouchstart' in window,
            userAgent: userAgent
        };
    }

    /**
     * 检测网络信息
     */
    detectNetwork() {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        
        if (!connection) {
            return {
                effectiveType: 'unknown',
                downlink: 'unknown',
                rtt: 'unknown'
            };
        }

        return {
            effectiveType: connection.effectiveType || 'unknown',
            downlink: connection.downlink || 'unknown',
            rtt: connection.rtt || 'unknown',
            saveData: connection.saveData || false
        };
    }

    /**
     * 生成播放器配置
     */
    generateConfig() {
        const config = {
            // 基础配置
            controls: true,
            playsinline: true,
            preload: 'metadata',
            
            // 样式配置
            width: '100%',
            height: 'auto',
            
            // 功能配置
            enableKeyboardShortcuts: true,
            enableTouchGestures: this.deviceInfo.touchSupport,
            enablePictureInPicture: !this.deviceInfo.isMobile,
            
            // 性能配置
            enableHardwareAcceleration: true,
            bufferSize: this.getOptimalBufferSize(),
            
            // 错误处理
            retryAttempts: 3,
            retryDelay: 1000
        };

        // 移动端特殊配置
        if (this.deviceInfo.isMobile) {
            config.maxHeight = this.deviceInfo.screenHeight * 0.6 + 'px';
            config.preload = this.networkInfo.saveData ? 'none' : 'metadata';
            config.enableAutoFullscreen = false;
            
            // iOS特殊配置
            if (this.deviceInfo.isIOS) {
                config.playsinline = true;
                config.webkitPlaysinline = true;
                config.enableInlinePlayback = true;
            }
            
            // Android特殊配置
            if (this.deviceInfo.isAndroid) {
                config.x5VideoPlayerType = 'h5';
                config.x5VideoPlayerFullscreen = true;
                config.x5VideoOrientation = 'portraint';
            }
        } else {
            // 桌面端配置
            config.maxHeight = '70vh';
            config.maxWidth = '800px';
        }

        // 网络优化配置
        if (this.networkInfo.effectiveType) {
            switch (this.networkInfo.effectiveType) {
                case 'slow-2g':
                case '2g':
                    config.preload = 'none';
                    config.bufferSize = 'small';
                    break;
                case '3g':
                    config.preload = 'metadata';
                    config.bufferSize = 'medium';
                    break;
                case '4g':
                default:
                    config.preload = 'metadata';
                    config.bufferSize = 'large';
                    break;
            }
        }

        return config;
    }

    /**
     * 获取最优缓冲区大小
     */
    getOptimalBufferSize() {
        if (this.deviceInfo.isMobile) {
            if (this.networkInfo.effectiveType === 'slow-2g' || this.networkInfo.effectiveType === '2g') {
                return 'small'; // 2-5秒
            } else if (this.networkInfo.effectiveType === '3g') {
                return 'medium'; // 5-10秒
            } else {
                return 'large'; // 10-30秒
            }
        } else {
            return 'large'; // 桌面端使用大缓冲区
        }
    }

    /**
     * 应用配置到视频元素
     */
    applyToVideoElement(videoElement) {
        if (!videoElement) return;

        // 应用基础属性
        videoElement.controls = this.config.controls;
        videoElement.preload = this.config.preload;
        
        if (this.config.playsinline) {
            videoElement.setAttribute('playsinline', 'true');
        }
        
        if (this.config.webkitPlaysinline) {
            videoElement.setAttribute('webkit-playsinline', 'true');
        }

        // 应用移动端特殊属性
        if (this.deviceInfo.isMobile) {
            if (this.config.x5VideoPlayerType) {
                videoElement.setAttribute('x5-video-player-type', this.config.x5VideoPlayerType);
            }
            if (this.config.x5VideoPlayerFullscreen) {
                videoElement.setAttribute('x5-video-player-fullscreen', 'true');
            }
            if (this.config.x5VideoOrientation) {
                videoElement.setAttribute('x5-video-orientation', this.config.x5VideoOrientation);
            }
        }

        // 应用样式
        if (this.config.maxHeight) {
            videoElement.style.maxHeight = this.config.maxHeight;
        }
        if (this.config.maxWidth) {
            videoElement.style.maxWidth = this.config.maxWidth;
        }
        
        videoElement.style.width = this.config.width;
        videoElement.style.height = this.config.height;
        videoElement.style.objectFit = 'contain';

        // 启用硬件加速
        if (this.config.enableHardwareAcceleration) {
            videoElement.style.transform = 'translateZ(0)';
            videoElement.style.backfaceVisibility = 'hidden';
        }

        console.log('视频播放器配置已应用:', this.config);
    }

    /**
     * 获取错误重试配置
     */
    getRetryConfig() {
        return {
            attempts: this.config.retryAttempts,
            delay: this.config.retryDelay,
            backoff: true // 指数退避
        };
    }

    /**
     * 获取设备信息
     */
    getDeviceInfo() {
        return this.deviceInfo;
    }

    /**
     * 获取网络信息
     */
    getNetworkInfo() {
        return this.networkInfo;
    }

    /**
     * 获取完整配置
     */
    getConfig() {
        return this.config;
    }

    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        return this.config;
    }

    /**
     * 监听网络变化
     */
    watchNetworkChanges(callback) {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        
        if (connection && typeof callback === 'function') {
            connection.addEventListener('change', () => {
                this.networkInfo = this.detectNetwork();
                this.config = this.generateConfig();
                callback(this.networkInfo, this.config);
            });
        }
    }

    /**
     * 监听屏幕方向变化
     */
    watchOrientationChanges(callback) {
        if (typeof callback === 'function') {
            window.addEventListener('orientationchange', () => {
                setTimeout(() => {
                    this.deviceInfo.screenWidth = window.innerWidth;
                    this.deviceInfo.screenHeight = window.innerHeight;
                    this.config = this.generateConfig();
                    callback(this.deviceInfo, this.config);
                }, 100);
            });
        }
    }
}

// 全局实例
window.videoPlayerConfig = new VideoPlayerConfig();

// 自动应用配置
document.addEventListener('DOMContentLoaded', function() {
    const videoElement = document.getElementById('video-player');
    if (videoElement && window.videoPlayerConfig) {
        window.videoPlayerConfig.applyToVideoElement(videoElement);
        
        // 输出调试信息
        console.log('设备信息:', window.videoPlayerConfig.getDeviceInfo());
        console.log('网络信息:', window.videoPlayerConfig.getNetworkInfo());
        console.log('播放器配置:', window.videoPlayerConfig.getConfig());
    }
});

// 导出类
window.VideoPlayerConfig = VideoPlayerConfig;
