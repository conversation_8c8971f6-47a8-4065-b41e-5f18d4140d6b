# API路径问题排查指南

## 🔍 问题现状

### 错误信息
```
NoResourceFoundException: No static resource api/video-error-report
```

### 问题分析
Spring Boot将API请求当作静态资源处理，而不是路由到控制器。

## 🛠️ 解决方案

### 方案1: 修改API路径 (已实施)

#### 新的API端点
- **旧路径**: `/api/video-error-report` (有问题)
- **新路径**: `/api/videos/error-report` (已修复)

#### 修改内容
1. **VideoApi.java**: 添加了 `@PostMapping("/error-report")` 方法
2. **video-diagnostics.js**: 更新请求路径为 `/api/videos/error-report`
3. **api-test.js**: 更新测试路径

### 方案2: 测试验证

#### 快速测试步骤
1. 访问 `/api-test` 页面
2. 点击"测试 /api/videos/error-report"按钮
3. 查看测试结果

#### 预期结果
- ✅ 新路径应该返回成功响应
- ❌ 旧路径仍然会失败

## 🧪 测试页面使用

### 访问测试页面
```
http://localhost:5000/api-test
```

### 测试功能
1. **错误报告API测试**
   - 测试新路径: `/api/videos/error-report`
   - 测试旧路径: `/api/video-error-report` (用于对比)

2. **视频API测试**
   - 健康检查: `/api/videos/health`
   - 视频列表: `/api/videos`

3. **自动测试**
   - 页面加载后自动运行基础测试
   - 实时显示测试结果

## 🔧 手动验证方法

### 使用curl测试
```bash
# 测试新的错误报告API
curl -X POST http://localhost:5000/api/videos/error-report \
  -H "Content-Type: application/json" \
  -d '{"错误时间":"2024-01-01","错误代码":"test","错误信息":"测试"}'

# 测试视频健康检查
curl -X GET http://localhost:5000/api/videos/health
```

### 使用浏览器开发者工具
1. 打开浏览器开发者工具 (F12)
2. 切换到 Network 标签
3. 访问 `/api-test` 页面
4. 观察网络请求和响应

## 📊 预期测试结果

### 成功情况
```json
{
  "success": true,
  "message": "错误报告已收到并记录",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 失败情况
```
404 Not Found - NoResourceFoundException
```

## 🔍 进一步排查

### 如果新路径仍然失败

#### 检查1: 控制器是否被扫描
```bash
# 查看启动日志，确认VideoApi被加载
grep -i "VideoApi" application.log
```

#### 检查2: 路径映射
```bash
# 访问Spring Boot Actuator端点 (如果启用)
curl http://localhost:5000/actuator/mappings
```

#### 检查3: 静态资源配置
查看 `WebConfig.java` 中的 `addResourceHandlers` 方法是否过于宽泛。

### 如果问题持续存在

#### 临时解决方案
1. **禁用错误报告**: 在JavaScript中注释掉错误报告发送代码
2. **使用本地存储**: 错误报告仅保存在本地，不发送到服务器
3. **简化路径**: 使用更简单的API路径结构

#### 代码修改
```javascript
// 临时禁用错误报告发送
sendErrorReport(report) {
    console.log('错误报告 (仅本地):', report);
    this.storeErrorReportLocally(report);
    // 注释掉网络请求
    // fetch('/api/videos/error-report', {...});
}
```

## 📝 验证清单

### 基础验证
- [ ] 访问 `/api-test` 页面正常加载
- [ ] 新路径 `/api/videos/error-report` 测试成功
- [ ] 视频健康检查 `/api/videos/health` 正常
- [ ] 服务器日志中能看到错误报告记录

### 功能验证
- [ ] 视频播放页面不再出现 NoResourceFoundException
- [ ] 错误报告能正常发送到服务器
- [ ] 诊断工具正常工作
- [ ] 本地存储备份机制正常

### 性能验证
- [ ] API响应时间正常 (<1秒)
- [ ] 没有额外的网络错误
- [ ] 内存使用正常
- [ ] 日志记录适量

## 🎯 最终目标

1. **✅ 解决API路径冲突问题**
2. **✅ 错误报告正常发送到服务器**
3. **✅ 提供完整的测试和验证工具**
4. **✅ 确保视频播放器功能不受影响**

## 📞 如果仍有问题

### 收集信息
1. 浏览器控制台的完整错误信息
2. 服务器日志中的相关错误
3. 网络请求的详细信息 (Headers, Body, Response)
4. 测试页面的结果截图

### 联系支持
提供上述信息以便进一步诊断和解决问题。
