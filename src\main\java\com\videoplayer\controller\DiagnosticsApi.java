package com.videoplayer.controller;

import com.videoplayer.common.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 诊断API控制器
 * 处理视频播放器的诊断和错误报告
 */
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class DiagnosticsApi {

    private static final Logger logger = LoggerFactory.getLogger(DiagnosticsApi.class);

    /**
     * 接收视频错误报告
     */
    @PostMapping("/video-error-report")
    public ResponseEntity<ApiResponse<String>> receiveErrorReport(@RequestBody Map<String, Object> errorReport) {
        try {
            logger.error("收到视频错误报告: {}", errorReport);
            
            // 记录详细的错误信息
            logErrorDetails(errorReport);
            
            // 这里可以将错误报告保存到数据库或发送到监控系统
            // 例如：errorReportService.saveErrorReport(errorReport);
            
            return ResponseEntity.ok(ApiResponse.success("错误报告已收到并记录"));
        } catch (Exception e) {
            logger.error("处理错误报告失败", e);
            return ResponseEntity.status(500).body(ApiResponse.error("REPORT_FAILED", "处理错误报告失败: " + e.getMessage()));
        }
    }

    /**
     * 接收性能报告
     */
    @PostMapping("/performance-report")
    public ResponseEntity<ApiResponse<String>> receivePerformanceReport(@RequestBody Map<String, Object> performanceReport) {
        try {
            logger.info("收到性能报告: {}", performanceReport);
            
            // 记录性能数据
            logPerformanceDetails(performanceReport);
            
            return ResponseEntity.ok(ApiResponse.success("性能报告已收到"));
        } catch (Exception e) {
            logger.error("处理性能报告失败", e);
            return ResponseEntity.status(500).body(ApiResponse.error("PERFORMANCE_REPORT_FAILED", "处理性能报告失败"));
        }
    }

    /**
     * 健康检查端点
     */
    @GetMapping("/diagnostics/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> diagnosticsHealth() {
        try {
            Map<String, Object> healthInfo = Map.of(
                "status", "healthy",
                "timestamp", LocalDateTime.now(),
                "service", "video-diagnostics",
                "version", "1.0.0"
            );
            
            return ResponseEntity.ok(ApiResponse.success("诊断服务正常", healthInfo));
        } catch (Exception e) {
            logger.error("诊断健康检查失败", e);
            return ResponseEntity.status(500).body(ApiResponse.error("HEALTH_CHECK_FAILED", "诊断服务异常"));
        }
    }

    /**
     * 获取诊断配置
     */
    @GetMapping("/diagnostics/config")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getDiagnosticsConfig() {
        try {
            Map<String, Object> config = Map.of(
                "errorReportEnabled", true,
                "performanceReportEnabled", true,
                "logLevel", "INFO",
                "retryAttempts", 3,
                "timeoutSeconds", 30
            );
            
            return ResponseEntity.ok(ApiResponse.success("获取诊断配置成功", config));
        } catch (Exception e) {
            logger.error("获取诊断配置失败", e);
            return ResponseEntity.status(500).body(ApiResponse.error("CONFIG_FAILED", "获取诊断配置失败"));
        }
    }

    /**
     * 记录错误详情
     */
    private void logErrorDetails(Map<String, Object> errorReport) {
        try {
            Object errorTime = errorReport.get("错误时间");
            Object errorCode = errorReport.get("错误代码");
            Object errorMessage = errorReport.get("错误信息");
            Object videoSrc = errorReport.get("视频源");
            Object browserInfo = errorReport.get("浏览器");
            Object deviceInfo = errorReport.get("设备");
            Object networkInfo = errorReport.get("网络");

            logger.error("=== 视频错误详情 ===");
            logger.error("时间: {}", errorTime);
            logger.error("错误代码: {}", errorCode);
            logger.error("错误信息: {}", errorMessage);
            logger.error("视频源: {}", videoSrc);
            logger.error("浏览器: {}", browserInfo);
            logger.error("设备: {}", deviceInfo);
            logger.error("网络: {}", networkInfo);
            logger.error("==================");

        } catch (Exception e) {
            logger.warn("记录错误详情时出现异常", e);
        }
    }

    /**
     * 记录性能详情
     */
    private void logPerformanceDetails(Map<String, Object> performanceReport) {
        try {
            Object loadTime = performanceReport.get("loadTime");
            Object playTime = performanceReport.get("playTime");
            Object bufferEvents = performanceReport.get("bufferEvents");
            Object networkType = performanceReport.get("networkType");

            logger.info("=== 视频性能报告 ===");
            logger.info("加载时间: {}", loadTime);
            logger.info("播放时间: {}", playTime);
            logger.info("缓冲事件: {}", bufferEvents);
            logger.info("网络类型: {}", networkType);
            logger.info("==================");

        } catch (Exception e) {
            logger.warn("记录性能详情时出现异常", e);
        }
    }
}
