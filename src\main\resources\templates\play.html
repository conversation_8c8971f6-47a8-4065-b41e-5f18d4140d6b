<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    <meta name="msapplication-navbutton-color" content="#ffffff">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="supported-color-schemes" content="light">
    <title th:text="${pageTitle}">视频播放</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">




    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/play-style.css" rel="stylesheet">




    </style>
</head>
<body style="background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important;">
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 搜索框 -->
                <div class="search-container">
                    <form class="d-flex search-form" action="/search" method="get">
                        <input class="form-control me-2 search-input-mobile" type="search" name="keyword" placeholder="搜索...">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm ms-1" type="button" onclick="toggleSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </form>
                    <button class="btn btn-outline-light btn-sm search-toggle" onclick="toggleSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-fluid px-3 py-4">
        <div class="row justify-content-center">
            <!-- 视频播放区域 -->
            <div class="col-12 col-md-8 col-lg-6">
                <!-- 视频播放器容器 -->
                <div class="video-player-container mb-4">
                    <div class="video-wrapper">
                        <!-- 响应式视频播放器 -->
                        <video
                            id="video-player"
                            controls
                            preload="metadata"
                            playsinline
                            webkit-playsinline
                            x5-video-player-type="h5"
                            x5-video-player-fullscreen="true"
                            x5-video-orientation="portraint"
                            th:data-video-url="${video.videoUrl}"
                            th:data-video-id="${video.id}"
                            th:poster="${video.thumbnailUrl}"
                            crossorigin="anonymous">
                            <!-- 多格式支持 -->
                            <source th:src="${video.videoUrl}" type="video/mp4">
                            <source th:src="${video.videoUrl}" type="video/webm">
                            <source th:src="${video.videoUrl}" type="video/ogg">
                            您的浏览器不支持HTML5视频播放。请升级您的浏览器或使用其他浏览器。
                        </video>









                    </div>
                </div>

                <!-- 视频信息 -->
                <div class="video-info bg-white rounded-3 shadow-sm p-4 mb-4">
                    <h1 class="video-title h4 mb-3" th:text="${video.title}">视频标题</h1>
                    <div class="video-stats">
                        <time class="video-date text-muted" th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd')}">2024-01-01</time>
                    </div>
                    <!-- 视频描述 -->
                    <div class="video-description mt-3" th:if="${video.description}">
                        <p class="text-muted mb-0" th:text="${video.description}">视频描述</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <!-- <h5><i class="fas fa-play-circle me-2"></i>佳茵轻康</h5> -->
                    <p class="mb-0">轻康自然，享瘦生活。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">联系我们&nbsp;&nbsp;&nbsp;</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>


    <!-- 自定义JS -->
    <script src="/js/main.js"></script>
    <script src="/js/video-player-config.js"></script>
    <script src="/js/video-diagnostics.js"></script>
    <script src="/js/mobile-video-player.js"></script>


    <script th:inline="javascript">
        // 优化的视频播放器初始化
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');
            if (!videoElement) return;

            const videoUrl = videoElement.dataset.videoUrl;
            const videoId = videoElement.dataset.videoId;

            // 移动端优化设置
            if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                videoElement.setAttribute('playsinline', 'true');
                videoElement.setAttribute('webkit-playsinline', 'true');
                videoElement.setAttribute('x5-video-player-type', 'h5');
                videoElement.setAttribute('x5-video-player-fullscreen', 'true');

                // 禁用自动全屏（iOS Safari）
                videoElement.setAttribute('x5-video-orientation', 'portraint');
            }

            // 验证和设置视频源
            if (videoUrl) {
                console.log('设置视频源:', videoUrl);

                // 检查URL格式
                if (!isValidVideoUrl(videoUrl)) {
                    console.error('无效的视频URL:', videoUrl);
                    showError('视频地址格式不正确', ['请检查视频链接', '联系管理员']);
                    return;
                }

                videoElement.src = videoUrl;

                // 添加源加载超时检测
                const loadTimeout = setTimeout(() => {
                    if (videoElement.readyState === 0) {
                        console.error('视频加载超时');
                        showError('视频加载超时', ['请检查网络连接', '尝试刷新页面']);
                    }
                }, 30000); // 30秒超时

                videoElement.addEventListener('loadstart', () => {
                    clearTimeout(loadTimeout);
                });
            } else {
                console.error('视频URL为空');
                showError('视频地址不存在', ['请联系管理员']);
                return;
            }

            // URL验证函数
            function isValidVideoUrl(url) {
                if (!url || typeof url !== 'string') return false;

                const videoExtensions = /\.(mp4|webm|ogg|avi|mov|m4v|3gp|mkv)(\?.*)?$/i;
                const httpPattern = /^https?:\/\/.+/i;

                return httpPattern.test(url) && (videoExtensions.test(url) || url.includes('blob:'));
            }

            // 通用错误显示函数
            function showError(message, suggestions = []) {
                hideLoadingIndicator();

                const existingError = videoElement.parentNode.querySelector('.video-error-message');
                if (existingError) {
                    existingError.remove();
                }

                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger mt-3 video-error-message';
                let suggestionHtml = suggestions.length > 0 ?
                    '<br><small class="text-muted">建议：' + suggestions.join('，') + '</small>' : '';

                errorDiv.innerHTML = `
                    <div class="d-flex align-items-start">
                        <i class="fas fa-exclamation-triangle me-2 mt-1"></i>
                        <div class="flex-grow-1">
                            <strong>${message}</strong>
                            ${suggestionHtml}
                            <br><button class="btn btn-sm btn-outline-danger mt-2" onclick="location.reload()">重新加载</button>
                        </div>
                    </div>
                `;

                videoElement.parentNode.appendChild(errorDiv);
            }

            // 添加加载指示器
            function showLoadingIndicator() {
                const loadingDiv = document.createElement('div');
                loadingDiv.id = 'video-loading';
                loadingDiv.className = 'position-absolute top-50 start-50 translate-middle text-white';
                loadingDiv.innerHTML = '<i class="fas fa-spinner fa-spin fa-2x"></i><br><small>加载中...</small>';
                videoElement.parentNode.appendChild(loadingDiv);
            }

            function hideLoadingIndicator() {
                const loadingDiv = document.getElementById('video-loading');
                if (loadingDiv) {
                    loadingDiv.remove();
                }
            }

            // 生成视频首帧缩略图
            function generateThumbnail() {
                if (!videoElement.poster) {
                    videoElement.addEventListener('loadeddata', function() {
                        try {
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');
                            canvas.width = videoElement.videoWidth;
                            canvas.height = videoElement.videoHeight;
                            ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
                            const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.8);
                            videoElement.poster = thumbnailDataUrl;
                            console.log('视频首帧缩略图生成成功');
                        } catch (error) {
                            console.warn('无法生成视频缩略图:', error);
                        }
                    });
                }
            }

            // 视频状态监控
            function logVideoState(event) {
                console.log(`视频事件: ${event}`, {
                    readyState: videoElement.readyState,
                    networkState: videoElement.networkState,
                    currentTime: videoElement.currentTime,
                    duration: videoElement.duration,
                    buffered: videoElement.buffered.length > 0 ?
                        `${videoElement.buffered.start(0)}-${videoElement.buffered.end(0)}` : 'none',
                    videoWidth: videoElement.videoWidth,
                    videoHeight: videoElement.videoHeight,
                    currentSrc: videoElement.currentSrc
                });
            }

            // 事件监听器
            videoElement.addEventListener('loadstart', function() {
                console.log('开始加载视频');
                logVideoState('loadstart');
                showLoadingIndicator();
                retryCount = 0; // 重置重试计数
            });

            videoElement.addEventListener('loadedmetadata', function() {
                console.log('视频元数据加载完成');
                logVideoState('loadedmetadata');

                // 检查视频尺寸
                if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
                    console.warn('视频尺寸异常:', videoElement.videoWidth, 'x', videoElement.videoHeight);
                }
            });

            videoElement.addEventListener('loadeddata', function() {
                console.log('视频数据加载完成');
                logVideoState('loadeddata');
                hideLoadingIndicator();
            });

            videoElement.addEventListener('canplay', function() {
                console.log('视频可以播放');
                logVideoState('canplay');
                hideLoadingIndicator();
            });

            videoElement.addEventListener('canplaythrough', function() {
                console.log('视频可以流畅播放');
                logVideoState('canplaythrough');
            });

            // 增强的错误处理
            let retryCount = 0;
            const maxRetries = 3;

            function handleVideoError(e) {
                console.error('视频播放出错:', e);
                console.error('错误详情:', {
                    error: videoElement.error,
                    networkState: videoElement.networkState,
                    readyState: videoElement.readyState,
                    currentSrc: videoElement.currentSrc
                });

                hideLoadingIndicator();

                let errorMessage = '视频加载失败';
                let suggestions = [];
                let canRetry = false;

                // 检查具体错误类型
                if (videoElement.error) {
                    const errorCode = videoElement.error.code;
                    console.error('错误代码:', errorCode, '错误信息:', videoElement.error.message);

                    switch (errorCode) {
                        case 1: // MEDIA_ERR_ABORTED
                            errorMessage = '视频加载被中止';
                            suggestions.push('请刷新页面重试');
                            canRetry = true;
                            break;
                        case 2: // MEDIA_ERR_NETWORK
                            errorMessage = '网络错误，无法加载视频';
                            suggestions.push('请检查网络连接', '尝试切换到WiFi网络');
                            canRetry = true;
                            break;
                        case 3: // MEDIA_ERR_DECODE
                            errorMessage = '视频解码失败或格式不支持';
                            suggestions.push('请尝试使用其他浏览器', '确保浏览器版本为最新');
                            break;
                        case 4: // MEDIA_ERR_SRC_NOT_SUPPORTED
                            errorMessage = '视频格式不支持或文件不存在';
                            suggestions.push('请联系管理员', '尝试其他视频');
                            break;
                        default:
                            errorMessage = '未知错误';
                            suggestions.push('请刷新页面重试');
                            canRetry = true;
                    }
                } else {
                    // 没有具体错误信息的情况
                    errorMessage = '视频加载失败';
                    suggestions.push('请检查网络连接', '刷新页面重试');
                    canRetry = true;
                }

                // 自动重试机制
                if (canRetry && retryCount < maxRetries) {
                    retryCount++;
                    console.log(`尝试重新加载视频 (${retryCount}/${maxRetries})`);

                    setTimeout(() => {
                        showLoadingIndicator();
                        videoElement.load(); // 重新加载视频
                    }, 2000 * retryCount); // 递增延迟

                    errorMessage += ` (正在重试 ${retryCount}/${maxRetries})`;
                } else if (retryCount >= maxRetries) {
                    errorMessage += ' (重试次数已达上限)';
                    suggestions.push('请联系技术支持');
                }

                // 显示错误信息
                const existingError = videoElement.parentNode.querySelector('.video-error-message');
                if (existingError) {
                    existingError.remove();
                }

                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger mt-3 video-error-message';
                let suggestionHtml = suggestions.length > 0 ?
                    '<br><small class="text-muted">建议：' + suggestions.join('，') + '</small>' : '';

                let retryButton = '';
                if (canRetry && retryCount < maxRetries) {
                    retryButton = '<br><button class="btn btn-sm btn-outline-danger mt-2" onclick="location.reload()">手动重试</button>';
                }

                errorDiv.innerHTML = `
                    <div class="d-flex align-items-start">
                        <i class="fas fa-exclamation-triangle me-2 mt-1"></i>
                        <div class="flex-grow-1">
                            <strong>${errorMessage}</strong>
                            ${suggestionHtml}
                            ${retryButton}
                        </div>
                    </div>
                `;

                videoElement.parentNode.appendChild(errorDiv);
            }

            videoElement.addEventListener('error', handleVideoError);

            // 播放事件
            videoElement.addEventListener('play', function() {
                console.log('视频开始播放，ID:', videoId);
            });

            videoElement.addEventListener('pause', function() {
                console.log('视频已暂停，ID:', videoId);
            });

            videoElement.addEventListener('ended', function() {
                console.log('视频播放结束，ID:', videoId);
            });

            // 缓冲事件
            videoElement.addEventListener('waiting', function() {
                console.log('视频缓冲中');
                showLoadingIndicator();
            });

            videoElement.addEventListener('playing', function() {
                console.log('视频恢复播放');
                hideLoadingIndicator();
            });

            // 生成缩略图
            generateThumbnail();

            console.log('优化的视频播放器已准备就绪');
        });
    </script>




</body>
</html>

