/**
 * CORS配置测试工具
 * 用于验证跨域配置是否正确
 */

class CorsTest {
    constructor() {
        this.testResults = [];
    }

    /**
     * 运行所有CORS测试
     */
    async runAllTests() {
        console.log('🚀 开始CORS配置测试...');
        
        const tests = [
            { name: '视频健康检查 (GET)', test: () => this.testGet('/api/videos/health') },
            { name: '错误报告 (POST)', test: () => this.testPost('/api/videos/error-report') },
            { name: '视频列表 (GET)', test: () => this.testGet('/api/videos?page=0&size=1') },
            { name: '诊断健康检查 (GET)', test: () => this.testGet('/api/diagnostics/health') }
        ];

        for (const { name, test } of tests) {
            console.log(`\n📋 测试: ${name}`);
            try {
                const result = await test();
                this.testResults.push({ name, success: result.success, details: result });
                console.log(result.success ? '✅ 通过' : '❌ 失败', result.message);
            } catch (error) {
                console.error(`❌ ${name} 测试异常:`, error);
                this.testResults.push({ name, success: false, error: error.message });
            }
        }

        this.printSummary();
        return this.testResults;
    }

    /**
     * 测试GET请求
     */
    async testGet(url) {
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            const corsHeaders = this.extractCorsHeaders(response);
            
            if (response.ok) {
                return {
                    success: true,
                    message: `GET ${url} 成功`,
                    status: response.status,
                    corsHeaders: corsHeaders
                };
            } else {
                return {
                    success: false,
                    message: `GET ${url} 失败: ${response.status} ${response.statusText}`,
                    status: response.status,
                    corsHeaders: corsHeaders
                };
            }
        } catch (error) {
            return {
                success: false,
                message: `GET ${url} 异常: ${error.message}`,
                error: error.message
            };
        }
    }

    /**
     * 测试POST请求
     */
    async testPost(url) {
        const testData = {
            "错误时间": new Date().toLocaleString(),
            "错误代码": "cors-test",
            "错误信息": "CORS配置测试",
            "视频源": "test-video.mp4"
        };

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            });

            const corsHeaders = this.extractCorsHeaders(response);
            
            if (response.ok) {
                return {
                    success: true,
                    message: `POST ${url} 成功`,
                    status: response.status,
                    corsHeaders: corsHeaders
                };
            } else {
                return {
                    success: false,
                    message: `POST ${url} 失败: ${response.status} ${response.statusText}`,
                    status: response.status,
                    corsHeaders: corsHeaders
                };
            }
        } catch (error) {
            return {
                success: false,
                message: `POST ${url} 异常: ${error.message}`,
                error: error.message
            };
        }
    }

    /**
     * 提取CORS相关的响应头
     */
    extractCorsHeaders(response) {
        const corsHeaders = {};
        const relevantHeaders = [
            'access-control-allow-origin',
            'access-control-allow-methods',
            'access-control-allow-headers',
            'access-control-allow-credentials',
            'access-control-max-age'
        ];

        relevantHeaders.forEach(header => {
            const value = response.headers.get(header);
            if (value) {
                corsHeaders[header] = value;
            }
        });

        return corsHeaders;
    }

    /**
     * 打印测试摘要
     */
    printSummary() {
        console.log('\n' + '='.repeat(50));
        console.log('📊 CORS测试结果摘要:');
        
        this.testResults.forEach(({ name, success, details, error }) => {
            const status = success ? '✅ 通过' : '❌ 失败';
            console.log(`  ${status} - ${name}`);
            
            if (details && details.corsHeaders) {
                console.log(`    CORS Headers:`, details.corsHeaders);
            }
            
            if (error) {
                console.log(`    错误: ${error}`);
            }
        });

        const successCount = this.testResults.filter(r => r.success).length;
        const totalCount = this.testResults.length;
        
        console.log(`\n🎯 总体结果: ${successCount}/${totalCount} 个测试通过`);
        
        if (successCount === totalCount) {
            console.log('🎉 所有CORS测试通过！');
        } else {
            console.log('⚠️ 部分CORS测试失败，请检查配置');
        }
    }

    /**
     * 测试预检请求 (OPTIONS)
     */
    async testPreflight(url) {
        try {
            const response = await fetch(url, {
                method: 'OPTIONS',
                headers: {
                    'Origin': window.location.origin,
                    'Access-Control-Request-Method': 'POST',
                    'Access-Control-Request-Headers': 'Content-Type'
                }
            });

            const corsHeaders = this.extractCorsHeaders(response);
            
            return {
                success: response.ok,
                message: `OPTIONS ${url} ${response.ok ? '成功' : '失败'}`,
                status: response.status,
                corsHeaders: corsHeaders
            };
        } catch (error) {
            return {
                success: false,
                message: `OPTIONS ${url} 异常: ${error.message}`,
                error: error.message
            };
        }
    }

    /**
     * 检查浏览器CORS支持
     */
    checkBrowserSupport() {
        const support = {
            fetch: typeof fetch !== 'undefined',
            cors: 'withCredentials' in new XMLHttpRequest(),
            headers: typeof Headers !== 'undefined'
        };

        console.log('🌐 浏览器CORS支持检查:', support);
        return support;
    }
}

// 全局实例
window.corsTest = new CorsTest();

// 添加快捷键测试 (Ctrl+Shift+C)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'C') {
        window.corsTest.runAllTests();
    }
});

// 页面加载完成后检查浏览器支持
document.addEventListener('DOMContentLoaded', function() {
    window.corsTest.checkBrowserSupport();
});

// 导出类
window.CorsTest = CorsTest;
