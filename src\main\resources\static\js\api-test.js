/**
 * API测试工具
 * 用于测试视频播放器相关的API端点
 */

class ApiTester {
    constructor() {
        this.baseUrl = window.location.origin;
    }

    /**
     * 测试错误报告API
     */
    async testErrorReportApi() {
        console.log('测试错误报告API...');
        
        const testErrorReport = {
            "错误时间": new Date().toLocaleString(),
            "错误代码": "test",
            "错误信息": "这是一个测试错误报告",
            "视频源": "test-video.mp4",
            "浏览器": {
                "userAgent": navigator.userAgent,
                "platform": navigator.platform
            },
            "设备": {
                "screenWidth": screen.width,
                "screenHeight": screen.height
            },
            "网络": {
                "available": true,
                "effectiveType": "4g"
            }
        };

        try {
            const response = await fetch('/api/video-error-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(testErrorReport)
            });

            if (response.ok) {
                const data = await response.json();
                console.log('✅ 错误报告API测试成功:', data);
                return true;
            } else {
                console.error('❌ 错误报告API测试失败:', response.status, response.statusText);
                return false;
            }
        } catch (error) {
            console.error('❌ 错误报告API测试异常:', error);
            return false;
        }
    }

    /**
     * 测试诊断健康检查API
     */
    async testDiagnosticsHealthApi() {
        console.log('测试诊断健康检查API...');
        
        try {
            const response = await fetch('/api/diagnostics/health', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('✅ 诊断健康检查API测试成功:', data);
                return true;
            } else {
                console.error('❌ 诊断健康检查API测试失败:', response.status, response.statusText);
                return false;
            }
        } catch (error) {
            console.error('❌ 诊断健康检查API测试异常:', error);
            return false;
        }
    }

    /**
     * 测试诊断配置API
     */
    async testDiagnosticsConfigApi() {
        console.log('测试诊断配置API...');
        
        try {
            const response = await fetch('/api/diagnostics/config', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('✅ 诊断配置API测试成功:', data);
                return true;
            } else {
                console.error('❌ 诊断配置API测试失败:', response.status, response.statusText);
                return false;
            }
        } catch (error) {
            console.error('❌ 诊断配置API测试异常:', error);
            return false;
        }
    }

    /**
     * 测试视频健康检查API
     */
    async testVideoHealthApi() {
        console.log('测试视频健康检查API...');
        
        try {
            const response = await fetch('/api/videos/health', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('✅ 视频健康检查API测试成功:', data);
                return true;
            } else {
                console.error('❌ 视频健康检查API测试失败:', response.status, response.statusText);
                return false;
            }
        } catch (error) {
            console.error('❌ 视频健康检查API测试异常:', error);
            return false;
        }
    }

    /**
     * 运行所有API测试
     */
    async runAllTests() {
        console.log('🚀 开始API测试...');
        console.log('='.repeat(50));
        
        const tests = [
            { name: '错误报告API', test: () => this.testErrorReportApi() },
            { name: '诊断健康检查API', test: () => this.testDiagnosticsHealthApi() },
            { name: '诊断配置API', test: () => this.testDiagnosticsConfigApi() },
            { name: '视频健康检查API', test: () => this.testVideoHealthApi() }
        ];

        const results = [];
        
        for (const { name, test } of tests) {
            console.log(`\n📋 测试: ${name}`);
            try {
                const result = await test();
                results.push({ name, success: result });
            } catch (error) {
                console.error(`❌ ${name} 测试异常:`, error);
                results.push({ name, success: false, error });
            }
        }

        console.log('\n' + '='.repeat(50));
        console.log('📊 测试结果汇总:');
        
        results.forEach(({ name, success, error }) => {
            const status = success ? '✅ 通过' : '❌ 失败';
            console.log(`  ${status} - ${name}`);
            if (error) {
                console.log(`    错误: ${error.message}`);
            }
        });

        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;
        
        console.log(`\n🎯 总体结果: ${successCount}/${totalCount} 个测试通过`);
        
        if (successCount === totalCount) {
            console.log('🎉 所有API测试通过！');
        } else {
            console.log('⚠️ 部分API测试失败，请检查服务器配置');
        }

        return results;
    }

    /**
     * 测试网络连接
     */
    async testNetworkConnection() {
        console.log('测试网络连接...');
        
        try {
            const response = await fetch('/api/videos/health', {
                method: 'GET',
                timeout: 5000
            });
            
            if (response.ok) {
                console.log('✅ 网络连接正常');
                return true;
            } else {
                console.log('⚠️ 网络连接异常:', response.status);
                return false;
            }
        } catch (error) {
            console.log('❌ 网络连接失败:', error.message);
            return false;
        }
    }
}

// 全局实例
window.apiTester = new ApiTester();

// 添加快捷键测试 (Ctrl+Shift+T)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'T') {
        window.apiTester.runAllTests();
    }
});

// 页面加载完成后自动测试网络连接
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.apiTester.testNetworkConnection();
    }, 2000);
});

// 导出类
window.ApiTester = ApiTester;
