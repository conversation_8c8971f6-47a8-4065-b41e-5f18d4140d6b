<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>API路径测试页面</h1>
    
    <div class="test-section">
        <h3>错误报告API测试</h3>
        <button onclick="testErrorReportApi()">测试 /api/videos/error-report</button>
        <button onclick="testOldErrorReportApi()">测试 /api/video-error-report (旧路径)</button>
    </div>
    
    <div class="test-section">
        <h3>视频API测试</h3>
        <button onclick="testVideoHealthApi()">测试 /api/videos/health</button>
        <button onclick="testVideoListApi()">测试 /api/videos (列表)</button>
    </div>

    <div class="test-section">
        <h3>CORS配置测试</h3>
        <button onclick="testCorsConfig()">测试CORS配置</button>
        <button onclick="checkBrowserSupport()">检查浏览器支持</button>
    </div>
    
    <div class="test-section">
        <h3>测试日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div id="log"></div>
    </div>

    <script src="/js/cors-test.js"></script>
    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#007bff',
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107'
            };
            
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `
                <span style="color: #6c757d;">[${timestamp}]</span>
                <span style="color: ${colors[type]}; font-weight: bold;">[${type.toUpperCase()}]</span>
                ${message}
            `;
            
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function testErrorReportApi() {
            log('测试新的错误报告API路径...', 'info');
            
            const testData = {
                "错误时间": new Date().toLocaleString(),
                "错误代码": "test",
                "错误信息": "API路径测试",
                "视频源": "test-video.mp4",
                "浏览器": { "userAgent": navigator.userAgent },
                "设备": { "screenWidth": screen.width },
                "网络": { "available": true }
            };
            
            try {
                const response = await fetch('/api/videos/error-report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 新路径测试成功: ${JSON.stringify(data)}`, 'success');
                } else {
                    log(`❌ 新路径测试失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ 新路径测试异常: ${error.message}`, 'error');
            }
        }
        
        async function testOldErrorReportApi() {
            log('测试旧的错误报告API路径...', 'info');
            
            const testData = {
                "错误时间": new Date().toLocaleString(),
                "错误代码": "test",
                "错误信息": "API路径测试",
                "视频源": "test-video.mp4"
            };
            
            try {
                const response = await fetch('/api/video-error-report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 旧路径测试成功: ${JSON.stringify(data)}`, 'success');
                } else {
                    log(`❌ 旧路径测试失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ 旧路径测试异常: ${error.message}`, 'error');
            }
        }
        
        async function testVideoHealthApi() {
            log('测试视频健康检查API...', 'info');
            
            try {
                const response = await fetch('/api/videos/health', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 健康检查成功: ${JSON.stringify(data)}`, 'success');
                } else {
                    log(`❌ 健康检查失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ 健康检查异常: ${error.message}`, 'error');
            }
        }
        
        async function testVideoListApi() {
            log('测试视频列表API...', 'info');
            
            try {
                const response = await fetch('/api/videos?page=0&size=5', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 视频列表获取成功: 找到 ${data.data ? data.data.length : 0} 个视频`, 'success');
                } else {
                    log(`❌ 视频列表获取失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ 视频列表获取异常: ${error.message}`, 'error');
            }
        }
        
        // CORS测试函数
        function testCorsConfig() {
            if (window.corsTest) {
                log('开始CORS配置测试...', 'info');
                window.corsTest.runAllTests().then(results => {
                    const successCount = results.filter(r => r.success).length;
                    const totalCount = results.length;
                    log(`CORS测试完成: ${successCount}/${totalCount} 通过`,
                        successCount === totalCount ? 'success' : 'warning');
                });
            } else {
                log('CORS测试工具未初始化', 'error');
            }
        }

        function checkBrowserSupport() {
            if (window.corsTest) {
                const support = window.corsTest.checkBrowserSupport();
                log(`浏览器支持检查: Fetch=${support.fetch}, CORS=${support.cors}, Headers=${support.headers}`, 'info');
            } else {
                log('CORS测试工具未初始化', 'error');
            }
        }

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始自动测试...', 'info');

            setTimeout(() => {
                testVideoHealthApi();
            }, 1000);

            setTimeout(() => {
                testErrorReportApi();
            }, 2000);

            setTimeout(() => {
                if (window.corsTest) {
                    checkBrowserSupport();
                }
            }, 3000);
        });
    </script>
</body>
</html>
