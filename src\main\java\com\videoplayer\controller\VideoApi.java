package com.videoplayer.controller;

import com.videoplayer.common.ApiResponse;
import com.videoplayer.entity.Video;
import com.videoplayer.exception.ResourceNotFoundException;
import com.videoplayer.service.VideoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 视频控制器
 */
@RestController
@RequestMapping("/api/videos")
@CrossOrigin(origins = "*")
public class VideoApi {

    private static final Logger logger = LoggerFactory.getLogger(VideoApi.class);

    @Autowired
    private VideoService videoService;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<String>> health() {
        try {
            long count = videoService.getTotalVideoCount();
            return ResponseEntity.ok(ApiResponse.success("系统正常，视频总数: " + count));
        } catch (Exception e) {
            logger.error("健康检查失败", e);
            return ResponseEntity.status(500).body(ApiResponse.error("HEALTH_CHECK_FAILED", "系统异常: " + e.getMessage()));
        }
    }

    /**
     * 获取所有视频列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<Video>>> getAllVideos(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        try {
            logger.info("获取视频列表 - page: {}, size: {}, sortBy: {}, sortDir: {}", page, size, sortBy, sortDir);

            Page<Video> videoPage = videoService.getAllActiveVideos(page, size, sortBy, sortDir);

            return ResponseEntity.ok(ApiResponse.success(
                videoPage.getContent(),
                videoPage.getTotalElements(),
                page,
                size
            ));
        } catch (Exception e) {
            logger.error("获取视频列表失败 - page: {}, size: {}, sortBy: {}, sortDir: {}", page, size, sortBy, sortDir, e);
            throw e; // 重新抛出异常，让GlobalExceptionHandler处理
        }
    }

    /**
     * 根据ID获取视频详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Video>> getVideoById(@PathVariable Long id) {
        logger.info("获取视频详情 - id: {}", id);
        
        Optional<Video> optionalVideo = videoService.getVideoById(id);
        
        if (optionalVideo.isEmpty()) {
            throw new ResourceNotFoundException("Video", id);
        }
        
        return ResponseEntity.ok(ApiResponse.success("获取视频详情成功", optionalVideo.get()));
    }

    /**
     * 搜索视频
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<Video>>> searchVideos(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        logger.info("搜索视频 - keyword: {}, page: {}, size: {}", keyword, page, size);
        
        Page<Video> videoPage = videoService.searchVideos(keyword, page, size);
        
        return ResponseEntity.ok(ApiResponse.success(
            videoPage.getContent(), 
            videoPage.getTotalElements(), 
            page, 
            size
        ));
    }

    /**
     * 添加视频
     */
    @PostMapping
    public ResponseEntity<ApiResponse<Video>> addVideo(@RequestBody Video video) {
        logger.info("添加视频 - title: {}", video.getTitle());

        Video savedVideo = videoService.saveVideo(video);

        return ResponseEntity.ok(ApiResponse.success("添加视频成功", savedVideo));
    }

    /**
     * 更新视频
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<Video>> updateVideo(@PathVariable Long id, @RequestBody Video video) {
        logger.info("更新视频 - id: {}", id);

        Video updatedVideo = videoService.updateVideo(id, video);
        if (updatedVideo == null) {
            throw new ResourceNotFoundException("Video", id);
        }

        return ResponseEntity.ok(ApiResponse.success("更新视频成功", updatedVideo));
    }

    /**
     * 删除视频
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<String>> deleteVideo(@PathVariable Long id) {
        logger.info("删除视频 - id: {}", id);

        boolean deleted = videoService.deleteVideo(id);
        if (!deleted) {
            throw new ResourceNotFoundException("Video", id);
        }

        return ResponseEntity.ok(ApiResponse.success("删除视频成功"));
    }

    /**
     * 接收视频错误报告 (临时放在这里解决路径问题)
     */
    @PostMapping("/error-report")
    public ResponseEntity<ApiResponse<String>> receiveErrorReport(@RequestBody Map<String, Object> errorReport) {
        try {
            logger.error("收到视频错误报告: {}", errorReport);

            // 记录详细的错误信息
            logErrorDetails(errorReport);

            return ResponseEntity.ok(ApiResponse.success("错误报告已收到并记录"));
        } catch (Exception e) {
            logger.error("处理错误报告失败", e);
            return ResponseEntity.status(500).body(ApiResponse.error("REPORT_FAILED", "处理错误报告失败: " + e.getMessage()));
        }
    }

    /**
     * 记录错误详情
     */
    private void logErrorDetails(Map<String, Object> errorReport) {
        try {
            Object errorTime = errorReport.get("错误时间");
            Object errorCode = errorReport.get("错误代码");
            Object errorMessage = errorReport.get("错误信息");
            Object videoSrc = errorReport.get("视频源");
            Object browserInfo = errorReport.get("浏览器");
            Object deviceInfo = errorReport.get("设备");
            Object networkInfo = errorReport.get("网络");

            logger.error("=== 视频错误详情 ===");
            logger.error("时间: {}", errorTime);
            logger.error("错误代码: {}", errorCode);
            logger.error("错误信息: {}", errorMessage);
            logger.error("视频源: {}", videoSrc);
            logger.error("浏览器: {}", browserInfo);
            logger.error("设备: {}", deviceInfo);
            logger.error("网络: {}", networkInfo);
            logger.error("==================");

        } catch (Exception e) {
            logger.warn("记录错误详情时出现异常", e);
        }
    }



    /**
     * 获取视频播放统计
     */
    @GetMapping("/play-stats/{id}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getPlayStats(@PathVariable Long id) {
        try {
            Optional<Video> videoOptional = videoService.getVideoById(id);

            if (videoOptional.isEmpty()) {
                throw new ResourceNotFoundException("Video", id);
            }

            Video video = videoOptional.get();

            // 构建播放统计信息
            Map<String, Object> stats = Map.of(
                "videoId", video.getId(),
                "title", video.getTitle(),
                "duration", video.getDuration(),
                "format", video.getVideoFormat(),
                "resolution", video.getResolution(),
                "fileSize", video.getFileSize(),
                "isActive", video.getIsActive(),
                "createdTime", video.getCreatedTime()
            );

            return ResponseEntity.ok(ApiResponse.success("获取播放统计成功", stats));
        } catch (Exception e) {
            logger.error("获取播放统计失败 - videoId: {}", id, e);
            throw e;
        }
    }
}
