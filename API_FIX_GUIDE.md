# API路径修复指南

## 🔧 问题解决

### 原始问题
```
NoResourceFoundException: No static resource api/video-error-report
```

### 问题原因
1. VideoApi的基础路径是`/api/videos`，导致错误报告端点变成`/api/videos/video-error-report`
2. JavaScript中请求的路径是`/api/video-error-report`，路径不匹配
3. Spring Boot将请求当作静态资源处理，而不是API端点

### 解决方案
创建了专门的`DiagnosticsApi`控制器，基础路径为`/api`，确保路径匹配。

## 🆕 新增API端点

### 1. 错误报告API
```
POST /api/video-error-report
Content-Type: application/json

{
  "错误时间": "2024-01-01 12:00:00",
  "错误代码": "4",
  "错误信息": "视频文件不存在",
  "视频源": "video.mp4",
  "浏览器": {...},
  "设备": {...},
  "网络": {...}
}
```

### 2. 性能报告API
```
POST /api/performance-report
Content-Type: application/json

{
  "loadTime": 2000,
  "playTime": 120,
  "bufferEvents": 3,
  "networkType": "4g"
}
```

### 3. 诊断健康检查
```
GET /api/diagnostics/health

Response:
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00",
    "service": "video-diagnostics",
    "version": "1.0.0"
  }
}
```

### 4. 诊断配置
```
GET /api/diagnostics/config

Response:
{
  "success": true,
  "data": {
    "errorReportEnabled": true,
    "performanceReportEnabled": true,
    "logLevel": "INFO",
    "retryAttempts": 3,
    "timeoutSeconds": 30
  }
}
```

## 🔍 错误处理增强

### 自动重试机制
```javascript
// 网络错误时自动重试，最多3次
if (canRetry && retryCount < maxRetries) {
    retryCount++;
    setTimeout(() => {
        videoElement.load();
    }, 2000 * retryCount); // 递增延迟
}
```

### 本地存储备份
```javascript
// 如果网络发送失败，本地存储错误报告
storeErrorReportLocally(report) {
    const key = `video-error-${Date.now()}`;
    localStorage.setItem(key, JSON.stringify(report));
}
```

### 详细错误信息
```javascript
// 根据错误代码提供具体建议
switch (videoElement.error.code) {
    case 1: // MEDIA_ERR_ABORTED
        errorMessage = '视频加载被中止';
        suggestions.push('请刷新页面重试');
        break;
    case 2: // MEDIA_ERR_NETWORK
        errorMessage = '网络错误，无法加载视频';
        suggestions.push('请检查网络连接', '尝试切换到WiFi网络');
        break;
    // ...更多错误类型
}
```

## 🧪 测试工具

### 1. 视频测试页面
访问 `/video-test` 查看完整的测试界面，包括：
- 实时视频状态监控
- 设备和网络信息显示
- 事件日志记录
- API测试功能

### 2. API测试工具
```javascript
// 手动运行API测试
window.apiTester.runAllTests();

// 或使用快捷键 Ctrl+Shift+T
```

### 3. 诊断面板
```javascript
// 显示诊断面板
window.videoDiagnostics.showDiagnosticPanel();

// 或使用快捷键 Ctrl+Shift+D
```

## 📊 监控和日志

### 服务器端日志
```java
// DiagnosticsApi会记录详细的错误信息
logger.error("=== 视频错误详情 ===");
logger.error("时间: {}", errorTime);
logger.error("错误代码: {}", errorCode);
logger.error("错误信息: {}", errorMessage);
logger.error("视频源: {}", videoSrc);
logger.error("==================");
```

### 客户端日志
```javascript
// 详细的事件记录
console.log(`[${timestamp}ms] 视频事件: ${eventType}`, eventData);

// 错误报告生成
console.error('视频错误报告:', report);
```

## 🚀 使用步骤

### 1. 开发环境测试
1. 启动应用程序
2. 访问 `/video-test` 页面
3. 点击"测试API"按钮验证所有端点
4. 观察控制台日志和事件记录

### 2. 生产环境部署
1. 确保所有API端点正常工作
2. 配置日志级别和监控
3. 设置错误报告收集系统
4. 监控视频播放质量指标

### 3. 问题排查
1. 查看浏览器控制台错误信息
2. 使用诊断面板分析详细数据
3. 检查服务器日志中的错误报告
4. 导出诊断数据进行深入分析

## 🔧 配置选项

### 客户端配置
```javascript
// 在video-player-config.js中调整
const config = {
    retryAttempts: 3,        // 重试次数
    retryDelay: 1000,        // 重试延迟
    timeoutSeconds: 30,      // 加载超时
    enableErrorReporting: true, // 启用错误报告
    enableLocalStorage: true    // 启用本地存储
};
```

### 服务器端配置
```yaml
# application.yml
logging:
  level:
    com.videoplayer.controller.DiagnosticsApi: DEBUG
```

## 📈 预期效果

1. **✅ API路径问题解决**: 错误报告能正常发送到服务器
2. **✅ 错误处理增强**: 自动重试和详细错误信息
3. **✅ 监控能力提升**: 完整的诊断和测试工具
4. **✅ 用户体验改善**: 更好的错误提示和恢复机制
5. **✅ 开发效率提高**: 便捷的测试和调试工具

现在您可以：
- 正常发送错误报告到服务器
- 使用完整的诊断工具排查问题
- 通过测试页面验证所有功能
- 获得详细的错误信息和解决建议
