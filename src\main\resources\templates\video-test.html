<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频播放器测试页面</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/play-style.css" rel="stylesheet">
    
    <style>
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
        }
        .diagnostic-panel {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-top: 1rem;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">
            <i class="fas fa-video me-2"></i>
            视频播放器测试页面
        </h1>
        
        <!-- 测试视频播放器 -->
        <div class="test-section">
            <h3>视频播放器测试</h3>
            <div class="row">
                <div class="col-md-8">
                    <div class="video-player-container">
                        <div class="video-wrapper">
                            <video
                                id="video-player"
                                controls
                                preload="metadata"
                                playsinline
                                webkit-playsinline
                                x5-video-player-type="h5"
                                x5-video-player-fullscreen="true"
                                x5-video-orientation="portraint"
                                crossorigin="anonymous">
                                <!-- 测试视频源 -->
                                <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                                <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.webm" type="video/webm">
                                您的浏览器不支持HTML5视频播放。
                            </video>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="diagnostic-panel">
                        <h5>实时状态</h5>
                        <div id="video-status">
                            <p><span class="status-indicator status-warning"></span>等待加载...</p>
                        </div>
                        <button class="btn btn-sm btn-primary" onclick="showDiagnostics()">
                            <i class="fas fa-chart-line me-1"></i>显示诊断
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="exportDiagnostics()">
                            <i class="fas fa-download me-1"></i>导出报告
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 设备信息 -->
        <div class="test-section">
            <h3>设备信息</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>浏览器信息</h5>
                    <div id="browser-info"></div>
                </div>
                <div class="col-md-6">
                    <h5>设备信息</h5>
                    <div id="device-info"></div>
                </div>
            </div>
        </div>
        
        <!-- 网络信息 -->
        <div class="test-section">
            <h3>网络信息</h3>
            <div id="network-info"></div>
        </div>
        
        <!-- 测试控制 -->
        <div class="test-section">
            <h3>测试控制</h3>
            <div class="btn-group" role="group">
                <button class="btn btn-success" onclick="testPlay()">
                    <i class="fas fa-play me-1"></i>播放测试
                </button>
                <button class="btn btn-warning" onclick="testPause()">
                    <i class="fas fa-pause me-1"></i>暂停测试
                </button>
                <button class="btn btn-info" onclick="testSeek()">
                    <i class="fas fa-forward me-1"></i>跳转测试
                </button>
                <button class="btn btn-danger" onclick="testError()">
                    <i class="fas fa-exclamation-triangle me-1"></i>错误测试
                </button>
            </div>
        </div>
        
        <!-- 事件日志 -->
        <div class="test-section">
            <h3>事件日志</h3>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="auto-scroll" checked>
                <label class="form-check-label" for="auto-scroll">
                    自动滚动
                </label>
            </div>
            <div id="event-log" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 1rem; border-radius: 0.5rem; font-family: monospace; font-size: 0.9rem;">
                <!-- 事件日志将在这里显示 -->
            </div>
            <button class="btn btn-sm btn-outline-secondary mt-2" onclick="clearLog()">
                <i class="fas fa-trash me-1"></i>清空日志
            </button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JS -->
    <script src="/js/main.js"></script>
    <script src="/js/video-player-config.js"></script>
    <script src="/js/video-diagnostics.js"></script>
    <script src="/js/mobile-video-player.js"></script>
    
    <script>
        let eventCount = 0;
        
        // 初始化测试页面
        document.addEventListener('DOMContentLoaded', function() {
            displayDeviceInfo();
            displayNetworkInfo();
            setupVideoStatusMonitoring();
            
            logEvent('页面加载完成', 'info');
        });
        
        // 显示设备信息
        function displayDeviceInfo() {
            const browserInfo = document.getElementById('browser-info');
            const deviceInfo = document.getElementById('device-info');
            
            if (window.videoPlayerConfig) {
                const config = window.videoPlayerConfig;
                const device = config.getDeviceInfo();
                const browser = device.userAgent;
                
                browserInfo.innerHTML = `
                    <p><strong>User Agent:</strong> ${browser}</p>
                    <p><strong>平台:</strong> ${device.platform || 'Unknown'}</p>
                    <p><strong>语言:</strong> ${device.language || 'Unknown'}</p>
                    <p><strong>在线状态:</strong> ${device.onLine ? '在线' : '离线'}</p>
                `;
                
                deviceInfo.innerHTML = `
                    <p><strong>屏幕尺寸:</strong> ${device.screenWidth} x ${device.screenHeight}</p>
                    <p><strong>窗口尺寸:</strong> ${device.windowWidth} x ${device.windowHeight}</p>
                    <p><strong>像素比:</strong> ${device.pixelRatio}</p>
                    <p><strong>触摸支持:</strong> ${device.touchSupport ? '是' : '否'}</p>
                    <p><strong>移动设备:</strong> ${device.isMobile ? '是' : '否'}</p>
                `;
            }
        }
        
        // 显示网络信息
        function displayNetworkInfo() {
            const networkInfo = document.getElementById('network-info');
            
            if (window.videoPlayerConfig) {
                const network = window.videoPlayerConfig.getNetworkInfo();
                
                if (network.available) {
                    networkInfo.innerHTML = `
                        <p><strong>网络类型:</strong> ${network.effectiveType || 'Unknown'}</p>
                        <p><strong>下行速度:</strong> ${network.downlink || 'Unknown'} Mbps</p>
                        <p><strong>往返时间:</strong> ${network.rtt || 'Unknown'} ms</p>
                        <p><strong>省流量模式:</strong> ${network.saveData ? '开启' : '关闭'}</p>
                    `;
                } else {
                    networkInfo.innerHTML = '<p>网络信息不可用</p>';
                }
            }
        }
        
        // 设置视频状态监控
        function setupVideoStatusMonitoring() {
            const video = document.getElementById('video-player');
            const statusDiv = document.getElementById('video-status');
            
            if (!video) return;
            
            function updateStatus() {
                const readyStates = ['无数据', '元数据', '当前数据', '未来数据', '足够数据'];
                const networkStates = ['空', '空闲', '加载中', '无源'];
                
                statusDiv.innerHTML = `
                    <p><span class="status-indicator ${video.error ? 'status-error' : video.readyState >= 3 ? 'status-success' : 'status-warning'}"></span>
                    就绪状态: ${readyStates[video.readyState] || '未知'}</p>
                    <p>网络状态: ${networkStates[video.networkState] || '未知'}</p>
                    <p>当前时间: ${video.currentTime.toFixed(2)}s</p>
                    <p>总时长: ${video.duration ? video.duration.toFixed(2) + 's' : '未知'}</p>
                    <p>是否暂停: ${video.paused ? '是' : '否'}</p>
                    ${video.error ? `<p class="text-danger">错误: ${video.error.message}</p>` : ''}
                `;
            }
            
            // 定期更新状态
            setInterval(updateStatus, 1000);
            
            // 监听视频事件
            const events = ['loadstart', 'loadedmetadata', 'loadeddata', 'canplay', 'play', 'pause', 'error', 'waiting', 'playing'];
            events.forEach(eventType => {
                video.addEventListener(eventType, () => {
                    updateStatus();
                    logEvent(`视频事件: ${eventType}`, 'video');
                });
            });
        }
        
        // 记录事件日志
        function logEvent(message, type = 'info') {
            const logDiv = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#007bff',
                video: '#28a745',
                error: '#dc3545',
                warning: '#ffc107'
            };
            
            eventCount++;
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `
                <span style="color: #6c757d;">[${timestamp}]</span>
                <span style="color: ${colors[type] || colors.info}; font-weight: bold;">[${eventCount}]</span>
                ${message}
            `;
            
            logDiv.appendChild(logEntry);
            
            // 自动滚动
            if (document.getElementById('auto-scroll').checked) {
                logDiv.scrollTop = logDiv.scrollHeight;
            }
        }
        
        // 测试函数
        function testPlay() {
            const video = document.getElementById('video-player');
            video.play().then(() => {
                logEvent('播放测试成功', 'info');
            }).catch(err => {
                logEvent('播放测试失败: ' + err.message, 'error');
            });
        }
        
        function testPause() {
            const video = document.getElementById('video-player');
            video.pause();
            logEvent('暂停测试执行', 'info');
        }
        
        function testSeek() {
            const video = document.getElementById('video-player');
            if (video.duration) {
                video.currentTime = video.duration * 0.5; // 跳转到中间
                logEvent('跳转测试: 跳转到50%位置', 'info');
            } else {
                logEvent('跳转测试失败: 视频未加载', 'warning');
            }
        }
        
        function testError() {
            const video = document.getElementById('video-player');
            video.src = 'invalid-url.mp4'; // 故意设置无效URL
            logEvent('错误测试: 设置无效视频源', 'warning');
        }
        
        function showDiagnostics() {
            if (window.videoDiagnostics) {
                window.videoDiagnostics.showDiagnosticPanel();
                logEvent('显示诊断面板', 'info');
            } else {
                logEvent('诊断工具未初始化', 'error');
            }
        }
        
        function exportDiagnostics() {
            if (window.videoDiagnostics) {
                window.videoDiagnostics.exportDiagnostics();
                logEvent('导出诊断报告', 'info');
            } else {
                logEvent('诊断工具未初始化', 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('event-log').innerHTML = '';
            eventCount = 0;
            logEvent('日志已清空', 'info');
        }
    </script>
</body>
</html>
