/**
 * 视频播放器诊断工具
 * Video Player Diagnostics Tool
 * <AUTHOR>
 * @version 1.0.0
 */

class VideoDiagnostics {
    constructor(videoElement) {
        this.video = videoElement;
        this.diagnosticData = {
            events: [],
            errors: [],
            performance: {},
            browser: this.getBrowserInfo(),
            device: this.getDeviceInfo(),
            network: this.getNetworkInfo()
        };
        this.startTime = Date.now();
        this.init();
    }

    /**
     * 初始化诊断工具
     */
    init() {
        this.setupEventListeners();
        this.startPerformanceMonitoring();
        console.log('视频诊断工具已启动');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        const events = [
            'loadstart', 'durationchange', 'loadedmetadata', 'loadeddata',
            'progress', 'canplay', 'canplaythrough', 'play', 'pause',
            'seeking', 'seeked', 'timeupdate', 'ended', 'error',
            'waiting', 'playing', 'stalled', 'suspend', 'abort',
            'emptied', 'ratechange', 'volumechange'
        ];

        events.forEach(eventType => {
            this.video.addEventListener(eventType, (e) => {
                this.logEvent(eventType, e);
            });
        });
    }

    /**
     * 记录事件
     */
    logEvent(eventType, event) {
        const timestamp = Date.now() - this.startTime;
        const eventData = {
            type: eventType,
            timestamp: timestamp,
            readyState: this.video.readyState,
            networkState: this.video.networkState,
            currentTime: this.video.currentTime,
            duration: this.video.duration || 0,
            buffered: this.getBufferedRanges(),
            error: this.video.error ? {
                code: this.video.error.code,
                message: this.video.error.message
            } : null
        };

        this.diagnosticData.events.push(eventData);

        // 特殊事件处理
        if (eventType === 'error') {
            this.handleError(event);
        } else if (eventType === 'loadedmetadata') {
            this.recordVideoInfo();
        }

        console.log(`[${timestamp}ms] 视频事件: ${eventType}`, eventData);
    }

    /**
     * 处理错误事件
     */
    handleError(event) {
        const errorData = {
            timestamp: Date.now() - this.startTime,
            error: this.video.error ? {
                code: this.video.error.code,
                message: this.video.error.message
            } : null,
            networkState: this.video.networkState,
            readyState: this.video.readyState,
            currentSrc: this.video.currentSrc,
            event: event
        };

        this.diagnosticData.errors.push(errorData);
        
        // 生成错误报告
        this.generateErrorReport(errorData);
    }

    /**
     * 生成错误报告
     */
    generateErrorReport(errorData) {
        const report = {
            错误时间: new Date().toLocaleString(),
            错误代码: errorData.error?.code || 'unknown',
            错误信息: errorData.error?.message || 'no message',
            网络状态: this.getNetworkStateText(errorData.networkState),
            就绪状态: this.getReadyStateText(errorData.readyState),
            视频源: errorData.currentSrc,
            浏览器: this.diagnosticData.browser,
            设备: this.diagnosticData.device,
            网络: this.diagnosticData.network,
            事件历史: this.diagnosticData.events.slice(-10) // 最近10个事件
        };

        console.error('视频错误报告:', report);
        
        // 可以发送到服务器进行分析
        this.sendErrorReport(report);
    }

    /**
     * 发送错误报告到服务器
     */
    sendErrorReport(report) {
        // 发送错误报告到服务器
        if (window.fetch) {
            fetch('/api/video-error-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(report)
            })
            .then(response => {
                if (response.ok) {
                    console.log('错误报告发送成功');
                    return response.json();
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            })
            .then(data => {
                console.log('服务器响应:', data);
            })
            .catch(err => {
                console.warn('发送错误报告失败:', err);
                // 如果发送失败，可以尝试本地存储
                this.storeErrorReportLocally(report);
            });
        } else {
            // 浏览器不支持fetch，使用本地存储
            this.storeErrorReportLocally(report);
        }
    }

    /**
     * 本地存储错误报告
     */
    storeErrorReportLocally(report) {
        try {
            const key = `video-error-${Date.now()}`;
            localStorage.setItem(key, JSON.stringify(report));
            console.log('错误报告已本地存储:', key);

            // 清理旧的错误报告（保留最近10个）
            this.cleanupLocalErrorReports();
        } catch (e) {
            console.warn('本地存储错误报告失败:', e);
        }
    }

    /**
     * 清理本地错误报告
     */
    cleanupLocalErrorReports() {
        try {
            const keys = Object.keys(localStorage)
                .filter(key => key.startsWith('video-error-'))
                .sort()
                .reverse(); // 最新的在前

            // 删除超过10个的旧报告
            if (keys.length > 10) {
                keys.slice(10).forEach(key => {
                    localStorage.removeItem(key);
                });
            }
        } catch (e) {
            console.warn('清理本地错误报告失败:', e);
        }
    }

    /**
     * 记录视频信息
     */
    recordVideoInfo() {
        this.diagnosticData.videoInfo = {
            duration: this.video.duration,
            videoWidth: this.video.videoWidth,
            videoHeight: this.video.videoHeight,
            currentSrc: this.video.currentSrc
        };
    }

    /**
     * 获取缓冲范围
     */
    getBufferedRanges() {
        const buffered = this.video.buffered;
        const ranges = [];
        
        for (let i = 0; i < buffered.length; i++) {
            ranges.push({
                start: buffered.start(i),
                end: buffered.end(i)
            });
        }
        
        return ranges;
    }

    /**
     * 开始性能监控
     */
    startPerformanceMonitoring() {
        setInterval(() => {
            this.updatePerformanceMetrics();
        }, 5000); // 每5秒更新一次
    }

    /**
     * 更新性能指标
     */
    updatePerformanceMetrics() {
        this.diagnosticData.performance = {
            timestamp: Date.now() - this.startTime,
            currentTime: this.video.currentTime,
            buffered: this.getBufferedRanges(),
            playbackRate: this.video.playbackRate,
            volume: this.video.volume,
            muted: this.video.muted,
            paused: this.video.paused,
            ended: this.video.ended
        };
    }

    /**
     * 获取浏览器信息
     */
    getBrowserInfo() {
        const ua = navigator.userAgent;
        return {
            userAgent: ua,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine
        };
    }

    /**
     * 获取设备信息
     */
    getDeviceInfo() {
        return {
            screenWidth: screen.width,
            screenHeight: screen.height,
            windowWidth: window.innerWidth,
            windowHeight: window.innerHeight,
            devicePixelRatio: window.devicePixelRatio,
            touchSupport: 'ontouchstart' in window
        };
    }

    /**
     * 获取网络信息
     */
    getNetworkInfo() {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        
        if (!connection) {
            return { available: false };
        }

        return {
            available: true,
            effectiveType: connection.effectiveType,
            downlink: connection.downlink,
            rtt: connection.rtt,
            saveData: connection.saveData
        };
    }

    /**
     * 获取网络状态文本
     */
    getNetworkStateText(state) {
        const states = {
            0: 'NETWORK_EMPTY',
            1: 'NETWORK_IDLE',
            2: 'NETWORK_LOADING',
            3: 'NETWORK_NO_SOURCE'
        };
        return states[state] || 'UNKNOWN';
    }

    /**
     * 获取就绪状态文本
     */
    getReadyStateText(state) {
        const states = {
            0: 'HAVE_NOTHING',
            1: 'HAVE_METADATA',
            2: 'HAVE_CURRENT_DATA',
            3: 'HAVE_FUTURE_DATA',
            4: 'HAVE_ENOUGH_DATA'
        };
        return states[state] || 'UNKNOWN';
    }

    /**
     * 获取诊断报告
     */
    getDiagnosticReport() {
        return {
            ...this.diagnosticData,
            summary: {
                totalEvents: this.diagnosticData.events.length,
                totalErrors: this.diagnosticData.errors.length,
                runningTime: Date.now() - this.startTime,
                lastEvent: this.diagnosticData.events[this.diagnosticData.events.length - 1]
            }
        };
    }

    /**
     * 导出诊断数据
     */
    exportDiagnostics() {
        const report = this.getDiagnosticReport();
        const blob = new Blob([JSON.stringify(report, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `video-diagnostics-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * 发送本地存储的错误报告
     */
    sendStoredErrorReports() {
        try {
            const keys = Object.keys(localStorage)
                .filter(key => key.startsWith('video-error-'));

            if (keys.length === 0) {
                console.log('没有本地存储的错误报告');
                return;
            }

            console.log(`发现 ${keys.length} 个本地存储的错误报告，开始发送...`);

            keys.forEach(key => {
                try {
                    const report = JSON.parse(localStorage.getItem(key));
                    this.sendErrorReport(report);
                    localStorage.removeItem(key); // 发送成功后删除
                } catch (e) {
                    console.warn(`发送本地错误报告失败: ${key}`, e);
                }
            });
        } catch (e) {
            console.warn('发送本地存储的错误报告时出错:', e);
        }
    }

    /**
     * 显示诊断面板
     */
    showDiagnosticPanel() {
        const report = this.getDiagnosticReport();
        console.table(report.events);
        console.log('完整诊断报告:', report);

        // 尝试发送本地存储的错误报告
        this.sendStoredErrorReports();

        // 可以创建一个可视化的诊断面板
        this.createDiagnosticUI(report);
    }

    /**
     * 创建诊断UI
     */
    createDiagnosticUI(report) {
        // 这里可以实现一个可视化的诊断界面
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            max-height: 400px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            overflow-y: auto;
            z-index: 9999;
            font-size: 12px;
        `;
        
        panel.innerHTML = `
            <h5>视频诊断面板</h5>
            <p><strong>事件数:</strong> ${report.summary.totalEvents}</p>
            <p><strong>错误数:</strong> ${report.summary.totalErrors}</p>
            <p><strong>运行时间:</strong> ${Math.round(report.summary.runningTime / 1000)}秒</p>
            <button onclick="this.parentNode.remove()">关闭</button>
            <button onclick="window.videoDiagnostics.exportDiagnostics()">导出</button>
        `;
        
        document.body.appendChild(panel);
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    const videoElement = document.getElementById('video-player');
    if (videoElement) {
        window.videoDiagnostics = new VideoDiagnostics(videoElement);
        
        // 添加快捷键显示诊断面板 (Ctrl+Shift+D)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                window.videoDiagnostics.showDiagnosticPanel();
            }
        });
    }
});

// 导出类
window.VideoDiagnostics = VideoDiagnostics;
