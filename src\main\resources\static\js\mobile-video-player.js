/**
 * 移动端视频播放器优化
 * Mobile Video Player Optimization
 * <AUTHOR>
 * @version 1.0.0
 */

class MobileVideoPlayer {
    constructor(videoElement) {
        this.video = videoElement;
        this.isMobile = this.detectMobile();
        this.isIOS = this.detectIOS();
        this.isAndroid = this.detectAndroid();
        this.init();
    }

    /**
     * 检测是否为移动设备
     */
    detectMobile() {
        return /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * 检测是否为iOS设备
     */
    detectIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }

    /**
     * 检测是否为Android设备
     */
    detectAndroid() {
        return /Android/i.test(navigator.userAgent);
    }

    /**
     * 初始化移动端优化
     */
    init() {
        if (!this.isMobile) return;

        this.setupMobileAttributes();
        this.setupTouchEvents();
        this.setupOrientationChange();
        this.setupNetworkOptimization();
        this.setupIOSOptimization();
        this.setupAndroidOptimization();

        console.log('移动端视频播放器优化已启用');
    }

    /**
     * 设置移动端属性
     */
    setupMobileAttributes() {
        // 基本移动端属性
        this.video.setAttribute('playsinline', 'true');
        this.video.setAttribute('webkit-playsinline', 'true');
        
        // 腾讯X5浏览器优化
        this.video.setAttribute('x5-video-player-type', 'h5');
        this.video.setAttribute('x5-video-player-fullscreen', 'true');
        this.video.setAttribute('x5-video-orientation', 'portraint');
        
        // 预加载策略优化
        this.video.preload = 'metadata';
        
        // 禁用画中画（移动端通常不需要）
        if ('disablePictureInPicture' in this.video) {
            this.video.disablePictureInPicture = true;
        }
    }

    /**
     * 设置触摸事件
     */
    setupTouchEvents() {
        let touchStartTime = 0;
        let touchStartX = 0;
        let touchStartY = 0;
        let isDoubleTap = false;

        // 双击播放/暂停
        this.video.addEventListener('touchstart', (e) => {
            const currentTime = Date.now();
            const touch = e.touches[0];
            
            if (currentTime - touchStartTime < 300) {
                isDoubleTap = true;
                this.togglePlayPause();
                e.preventDefault();
            }
            
            touchStartTime = currentTime;
            touchStartX = touch.clientX;
            touchStartY = touch.clientY;
        });

        // 防止意外的触摸事件
        this.video.addEventListener('touchend', (e) => {
            if (isDoubleTap) {
                isDoubleTap = false;
                e.preventDefault();
            }
        });

        // 滑动手势（可选功能）
        let startX = 0;
        this.video.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
        });

        this.video.addEventListener('touchmove', (e) => {
            if (!this.video.duration) return;
            
            const currentX = e.touches[0].clientX;
            const diff = currentX - startX;
            
            // 水平滑动调整进度（可选）
            if (Math.abs(diff) > 50) {
                const seekTime = (diff / window.innerWidth) * this.video.duration * 0.1;
                const newTime = Math.max(0, Math.min(this.video.duration, this.video.currentTime + seekTime));
                this.video.currentTime = newTime;
                startX = currentX;
            }
        });
    }

    /**
     * 设置屏幕方向变化处理
     */
    setupOrientationChange() {
        const handleOrientationChange = () => {
            setTimeout(() => {
                // 重新调整视频尺寸
                this.adjustVideoSize();
            }, 100);
        };

        window.addEventListener('orientationchange', handleOrientationChange);
        window.addEventListener('resize', handleOrientationChange);
    }

    /**
     * 调整视频尺寸
     */
    adjustVideoSize() {
        if (!this.isMobile) return;

        const isLandscape = window.innerWidth > window.innerHeight;
        
        if (isLandscape) {
            // 横屏时最大化视频
            this.video.style.maxHeight = '90vh';
        } else {
            // 竖屏时适中尺寸
            this.video.style.maxHeight = '50vh';
        }
    }

    /**
     * 网络优化
     */
    setupNetworkOptimization() {
        // 检测网络连接类型
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            const adjustQualityByConnection = () => {
                if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                    // 慢速网络：降低预加载
                    this.video.preload = 'none';
                } else if (connection.effectiveType === '3g') {
                    // 3G网络：元数据预加载
                    this.video.preload = 'metadata';
                } else {
                    // 4G/WiFi：正常预加载
                    this.video.preload = 'metadata';
                }
            };

            adjustQualityByConnection();
            connection.addEventListener('change', adjustQualityByConnection);
        }
    }

    /**
     * iOS特定优化
     */
    setupIOSOptimization() {
        if (!this.isIOS) return;

        // iOS Safari 特殊处理
        this.video.addEventListener('webkitbeginfullscreen', () => {
            console.log('iOS 进入全屏模式');
        });

        this.video.addEventListener('webkitendfullscreen', () => {
            console.log('iOS 退出全屏模式');
        });

        // 防止iOS自动全屏
        this.video.setAttribute('playsinline', 'true');
        this.video.setAttribute('webkit-playsinline', 'true');
    }

    /**
     * Android特定优化
     */
    setupAndroidOptimization() {
        if (!this.isAndroid) return;

        // Android Chrome 优化
        this.video.style.transform = 'translateZ(0)'; // 启用硬件加速
        
        // 处理Android的全屏问题
        this.video.addEventListener('fullscreenchange', () => {
            if (document.fullscreenElement) {
                // 进入全屏时的处理
                this.video.style.objectFit = 'contain';
            } else {
                // 退出全屏时的处理
                this.video.style.objectFit = 'contain';
            }
        });
    }

    /**
     * 切换播放/暂停
     */
    togglePlayPause() {
        if (this.video.paused) {
            this.video.play().catch(error => {
                console.error('播放失败:', error);
            });
        } else {
            this.video.pause();
        }
    }

    /**
     * 获取设备信息
     */
    getDeviceInfo() {
        return {
            isMobile: this.isMobile,
            isIOS: this.isIOS,
            isAndroid: this.isAndroid,
            userAgent: navigator.userAgent,
            screenSize: {
                width: window.innerWidth,
                height: window.innerHeight
            }
        };
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    const videoElement = document.getElementById('video-player');
    if (videoElement) {
        window.mobileVideoPlayer = new MobileVideoPlayer(videoElement);
        
        // 调试信息
        console.log('设备信息:', window.mobileVideoPlayer.getDeviceInfo());
    }
});

// 导出到全局作用域
window.MobileVideoPlayer = MobileVideoPlayer;
