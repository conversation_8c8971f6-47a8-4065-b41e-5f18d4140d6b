package com.videoplayer.service;

import com.videoplayer.entity.Video;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 视频兼容性服务
 * 处理不同设备和浏览器的视频格式兼容性问题
 */
@Service
public class VideoCompatibilityService {

    private static final Logger logger = LoggerFactory.getLogger(VideoCompatibilityService.class);

    // 支持的视频格式
    private static final List<String> SUPPORTED_FORMATS = Arrays.asList("mp4", "webm", "ogg", "avi", "mov");
    
    // 移动端优先格式
    private static final List<String> MOBILE_PREFERRED_FORMATS = Arrays.asList("mp4", "webm");
    
    // 桌面端优先格式
    private static final List<String> DESKTOP_PREFERRED_FORMATS = Arrays.asList("mp4", "webm", "ogg");

    /**
     * 检查视频URL是否有效
     */
    public boolean isValidVideoUrl(String videoUrl) {
        if (videoUrl == null || videoUrl.trim().isEmpty()) {
            return false;
        }

        // 检查URL格式
        Pattern urlPattern = Pattern.compile(
            "^(https?://)([\\w\\-\\.]+)([:\\d+])?(/.*)?\\.(mp4|webm|ogg|avi|mov)(\\?.*)?$",
            Pattern.CASE_INSENSITIVE
        );

        return urlPattern.matcher(videoUrl.trim()).matches();
    }

    /**
     * 获取视频格式
     */
    public String getVideoFormat(String videoUrl) {
        if (videoUrl == null) return null;
        
        String lowerUrl = videoUrl.toLowerCase();
        for (String format : SUPPORTED_FORMATS) {
            if (lowerUrl.contains("." + format)) {
                return format;
            }
        }
        return "unknown";
    }

    /**
     * 检查格式是否被移动端支持
     */
    public boolean isMobileCompatible(String format) {
        return MOBILE_PREFERRED_FORMATS.contains(format.toLowerCase());
    }

    /**
     * 检查格式是否被桌面端支持
     */
    public boolean isDesktopCompatible(String format) {
        return DESKTOP_PREFERRED_FORMATS.contains(format.toLowerCase());
    }

    /**
     * 为移动端优化视频URL
     */
    public String optimizeForMobile(String videoUrl) {
        if (!isValidVideoUrl(videoUrl)) {
            return videoUrl;
        }

        String format = getVideoFormat(videoUrl);
        
        // 如果已经是移动端兼容格式，直接返回
        if (isMobileCompatible(format)) {
            return videoUrl;
        }

        // 尝试转换为MP4格式（最兼容的移动端格式）
        String optimizedUrl = videoUrl.replaceAll("\\.(webm|ogg|avi|mov)$", ".mp4");
        
        logger.info("为移动端优化视频URL: {} -> {}", videoUrl, optimizedUrl);
        return optimizedUrl;
    }

    /**
     * 生成多格式视频源
     */
    public List<VideoSource> generateVideoSources(String primaryUrl) {
        List<VideoSource> sources = Arrays.asList(
            new VideoSource(primaryUrl, "video/mp4"),
            new VideoSource(primaryUrl.replaceAll("\\.(mp4|webm|ogg)$", ".webm"), "video/webm"),
            new VideoSource(primaryUrl.replaceAll("\\.(mp4|webm|ogg)$", ".ogg"), "video/ogg")
        );

        logger.debug("生成视频源列表: {}", sources);
        return sources;
    }

    /**
     * 检查视频是否需要转码
     */
    public boolean needsTranscoding(Video video) {
        String format = getVideoFormat(video.getVideoUrl());
        
        // 如果格式未知或不支持，需要转码
        if ("unknown".equals(format) || !SUPPORTED_FORMATS.contains(format)) {
            logger.warn("视频需要转码 - ID: {}, 格式: {}", video.getId(), format);
            return true;
        }

        return false;
    }

    /**
     * 获取推荐的视频设置
     */
    public VideoSettings getRecommendedSettings(String userAgent) {
        VideoSettings settings = new VideoSettings();
        
        if (isMobileUserAgent(userAgent)) {
            // 移动端设置
            settings.setPreload("metadata");
            settings.setAutoplay(false);
            settings.setControls(true);
            settings.setPlaysinline(true);
            settings.setMaxWidth("100%");
            settings.setMaxHeight("50vh");
        } else {
            // 桌面端设置
            settings.setPreload("metadata");
            settings.setAutoplay(false);
            settings.setControls(true);
            settings.setPlaysinline(false);
            settings.setMaxWidth("800px");
            settings.setMaxHeight("70vh");
        }

        return settings;
    }

    /**
     * 检查是否为移动端User-Agent
     */
    private boolean isMobileUserAgent(String userAgent) {
        if (userAgent == null) return false;
        
        Pattern mobilePattern = Pattern.compile(
            ".*(Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini).*",
            Pattern.CASE_INSENSITIVE
        );
        
        return mobilePattern.matcher(userAgent).matches();
    }

    /**
     * 视频源内部类
     */
    public static class VideoSource {
        private String url;
        private String type;

        public VideoSource(String url, String type) {
            this.url = url;
            this.type = type;
        }

        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }

        @Override
        public String toString() {
            return String.format("VideoSource{url='%s', type='%s'}", url, type);
        }
    }

    /**
     * 视频设置内部类
     */
    public static class VideoSettings {
        private String preload = "metadata";
        private boolean autoplay = false;
        private boolean controls = true;
        private boolean playsinline = false;
        private String maxWidth = "100%";
        private String maxHeight = "auto";

        // Getters and Setters
        public String getPreload() { return preload; }
        public void setPreload(String preload) { this.preload = preload; }
        public boolean isAutoplay() { return autoplay; }
        public void setAutoplay(boolean autoplay) { this.autoplay = autoplay; }
        public boolean isControls() { return controls; }
        public void setControls(boolean controls) { this.controls = controls; }
        public boolean isPlaysinline() { return playsinline; }
        public void setPlaysinline(boolean playsinline) { this.playsinline = playsinline; }
        public String getMaxWidth() { return maxWidth; }
        public void setMaxWidth(String maxWidth) { this.maxWidth = maxWidth; }
        public String getMaxHeight() { return maxHeight; }
        public void setMaxHeight(String maxHeight) { this.maxHeight = maxHeight; }
    }
}
